%% 4自由度R-P-P-R机器人 - 紫色机械臂末端斜直线运动
clear; clc; close all;
 
%% 机器人参数 
robot.L_blue = 1.0;         % 蓝色臂长度 
robot.W_blue = 0.06;        % 蓝色臂宽度 
robot.L_yellow = 0.15;      % 黄色滑块长度 
robot.W_yellow = 0.14;      % 黄色滑块宽度 
robot.H_yellow = 0.05;      % 黄色滑块高度 
robot.L_green = 0.6;        % 绿色臂长度 
robot.W_green = 0.04;       % 绿色臂宽度 
robot.L_purple = 0.1;       % 紫色执行器长度 
robot.W_purple = 0.07;      % 紫色执行器宽度 
robot.H_purple = 0.07;      % 紫色执行器高度 
 
% D-H参数表 
robot.dh_params = [ 
    0,       0,                                     robot.L_blue,   0;        % 关节1: 蓝色臂绕X轴旋转 
    0,       robot.W_blue/2 + robot.H_yellow/2,    0,              pi/2;     % 关节2: 黄色滑块沿X轴平移 
    0,       0,                                     0,              -pi/2;    % 关节3: 绿色臂沿Y轴平移 
    0,       robot.H_yellow/2 + robot.W_green/2,   robot.L_green + robot.L_purple,  pi/2;     % 关节4: 紫色机械臂绕X轴旋转，到达末端 
]; 
 
% 关节类型定义 (1=旋转, 0=平移) 
robot.joint_types = [1, 0, 0, 1];  % R-P-P-R 
 
% 关节限制 
green_slide_limit = (robot.W_yellow - robot.W_green) / 2; 
robot.q_limits = [ 
    -deg2rad(60),  deg2rad(60);     % q1: 蓝色臂绕X轴旋转 ±60° 
    0,             0.46;            % q2: 黄色滑块沿X轴平移 0-0.46m 
    -green_slide_limit, green_slide_limit;  % q3: 绿色臂Y轴平移 
    -pi,           pi               % q4: 紫色执行器绕X轴旋转 ±180° 
]; 

robot_offset_z = -0.6;  % 机器人整体下移偏移量
 
fprintf('=== 4自由度R-P-P-R机器人 - 紫色机械臂末端斜直线运动 ===\n');

%% 图形设置 
figure('Name', 'R-P-P-R Robot - Diagonal Line Motion', 'NumberTitle', 'off', 'Color', 'w', 'Position', [100, 100, 1200, 800]);
hold on; axis equal; grid on;
view(135, 25);
xlabel('X (m)'); ylabel('Y (m)'); zlabel('Z (m)');
title('4-DOF R-P-P-R Robot - Purple Arm End-Effector Diagonal Line Motion');
xlim([-0.2, 1.8]); ylim([-0.8, 0.8]); zlim([-1.2, 0.4]);
camlight; lighting gouraud;
 
%% 视频设置 
video_filename = 'rppr_robot_diagonal_line.mp4';
video_writer = VideoWriter(video_filename, 'MPEG-4'); 
video_writer.FrameRate = 20; 
video_writer.Quality = 95; 
open(video_writer); 
 
%% 创建图形对象 
unit_cube_vertices = [-0.5 -0.5 -0.5; 0.5 -0.5 -0.5; 0.5 0.5 -0.5; -0.5 0.5 -0.5; 
                      -0.5 -0.5 0.5; 0.5 -0.5 0.5; 0.5 0.5 0.5; -0.5 0.5 0.5]'; 
unit_cube_faces = [1 2 6 5; 2 3 7 6; 3 4 8 7; 4 1 5 8; 1 2 3 4; 5 6 7 8]; 
initial_vertices = zeros(8, 3); 
 
h_blue_arm = patch('Vertices', initial_vertices, 'Faces', unit_cube_faces, 'FaceColor', 'b', 'EdgeColor', 'k'); 
h_yellow_slider = patch('Vertices', initial_vertices, 'Faces', unit_cube_faces, 'FaceColor', 'y', 'EdgeColor', 'k'); 
h_green_arm = patch('Vertices', initial_vertices, 'Faces', unit_cube_faces, 'FaceColor', 'g', 'EdgeColor', 'k'); 
h_purple_effector = patch('Vertices', initial_vertices, 'Faces', unit_cube_faces, 'FaceColor', [0.6, 0.2, 0.8], 'EdgeColor', 'k'); 
 
% 参数显示文本框 
h_text = annotation('textbox', [0.78, 0.75, 0.2, 0.2], 'String', 'Ready', 'FontSize', 10, ... 
                   'VerticalAlignment', 'top', 'EdgeColor', 'k', 'BackgroundColor', 'w', 'FitBoxToText', 'on');

%% 动画仿真 - 紫色机械臂末端斜直线运动
fprintf('开始机器人运动动画仿真和视频录制（紫色机械臂末端斜直线运动）...\n');

dt = 0.05;
animation_duration = 10;
frame_count = 0;

% 定义斜直线运动的起点和终点
start_pos = [1.2, 0.1, -0.5];   % 起点位置
end_pos_target = [1.6, 0.4, -0.3];     % 终点位置

% 添加轨迹可视化
h_trajectory = plot3([], [], [], 'r-', 'LineWidth', 2);  % 红色轨迹线
trajectory_points = [];  % 存储轨迹点

for t = 0:dt:animation_duration
    % 计算斜直线运动的目标位置（线性插值）
    progress = t / animation_duration;  % 运动进度 0->1
    target_pos = start_pos + progress * (end_pos_target - start_pos);

    % 使用逆运动学求解关节角度
    q = inverse_kinematics_numerical(target_pos, robot, robot_offset_z);

    % 应用关节限制
    q = apply_joint_limits(q, robot);

    % 正运动学计算实际末端位置
    [~, T_end] = forward_kinematics_dh(q, robot);
    actual_end_pos = T_end(1:3, 4);
    actual_end_pos(3) = actual_end_pos(3) + robot_offset_z;

    % 存储轨迹点并更新轨迹显示
    trajectory_points = [trajectory_points; actual_end_pos'];
    set(h_trajectory, 'XData', trajectory_points(:,1), 'YData', trajectory_points(:,2), 'ZData', trajectory_points(:,3));

    % 绘制机器人
    draw_robot_dh_with_offset(q, robot, h_blue_arm, h_yellow_slider, h_green_arm, h_purple_effector, unit_cube_vertices, robot_offset_z);

    % 计算位置误差
    position_error = norm(target_pos - actual_end_pos');

    % 更新参数显示
    param_str = sprintf('紫色机械臂末端斜直线运动:\nq1: %+.1f°\nq2: %.3fm\nq3: %+.3fm\nq4: %.1f°\n\n目标位置:\nX: %.3f Y: %.3f Z: %.3f\n\n实际位置:\nX: %.3f Y: %.3f Z: %.3f\n\n位置误差: %.4f m\n进度: %.1f%%\n时间: %.1fs',...
                       rad2deg(q(1)), q(2), q(3), rad2deg(q(4)), ...
                       target_pos(1), target_pos(2), target_pos(3), ...
                       actual_end_pos(1), actual_end_pos(2), actual_end_pos(3), ...
                       position_error, progress*100, t);
    set(h_text, 'String', param_str);

    drawnow;

    % 录制视频帧
    frame = getframe(gcf);
    writeVideo(video_writer, frame);
    frame_count = frame_count + 1;

    pause(dt);
end
 
% 关闭视频文件 
close(video_writer); 
 
fprintf('机器人运动动画完成！\n'); 
fprintf('视频已保存为: %s\n', video_filename); 
fprintf('总帧数: %d\n', frame_count);

%% 正逆运动学验证
fprintf('\n=== 正逆运动学验证 ===\n'); 
 
% 测试关节角度
test_q = [deg2rad(30), 0.3, 0.02, deg2rad(-30)]; 
[~, T_test] = forward_kinematics_dh(test_q, robot); 
test_target = T_test(1:3, 4)'; 
test_target(3) = test_target(3) + robot_offset_z; 
 
fprintf('测试关节角度: q1=%.1f°, q2=%.3fm, q3=%.3fm, q4=%.1f°\n', ... 
        rad2deg(test_q(1)), test_q(2), test_q(3), rad2deg(test_q(4))); 
fprintf('正运动学目标位置: [%.3f, %.3f, %.3f]\n', test_target(1), test_target(2), test_target(3)); 

% 逆运动学求解
q_solution = inverse_kinematics_numerical(test_target, robot, robot_offset_z); 
fprintf('逆运动学解: q1=%.1f°, q2=%.3fm, q3=%.3fm, q4=%.1f°\n', ... 
        rad2deg(q_solution(1)), q_solution(2), q_solution(3), rad2deg(q_solution(4))); 
 
% 验证
[~, T_verify] = forward_kinematics_dh(q_solution, robot); 
actual_pos = T_verify(1:3, 4); 
actual_pos(3) = actual_pos(3) + robot_offset_z;
fprintf('验证位置: [%.3f, %.3f, %.3f]\n', actual_pos(1), actual_pos(2), actual_pos(3)); 
fprintf('位置误差: %.6f m\n', norm(test_target - actual_pos')); 

fprintf('\n=== 完成 ===\n');
fprintf('✓ 紫色机械臂末端斜直线运动动画完成\n');
fprintf('✓ 轨迹跟踪和逆运动学验证完成\n');
fprintf('✓ 视频文件: %s\n', video_filename);
fprintf('✓ 红色轨迹线显示了末端执行器的实际运动路径\n');

%% 数值逆运动学函数
function q_solution = inverse_kinematics_numerical(target_pos, robot, offset_z)
    % 数值逆运动学求解器
    
    % 初始猜测
    initial_guesses = [
        [0, 0.2, 0, 0];  % 中性位置
        [deg2rad(30), 0.3, 0.02, deg2rad(-30)];
        [deg2rad(-30), 0.1, -0.02, deg2rad(30)];
    ];
    
    % 优化设置
    options = optimoptions('fmincon', 'Display', 'off', 'Algorithm', 'sqp', ...
                          'MaxIterations', 3000, 'TolFun', 1e-12, 'TolX', 1e-12);
    
    objective = @(q) calc_position_error_with_offset(q, target_pos, robot, offset_z);
    
    best_solution = [];
    best_error = inf;
    
    % 尝试不同的初始猜测
    for i = 1:size(initial_guesses, 1)
        try
            q0 = initial_guesses(i, :)';
            [q_temp, fval] = fmincon(objective, q0, [], [], [], [], ...
                                   robot.q_limits(:,1), robot.q_limits(:,2), [], options);
            
            if fval < best_error
                best_error = fval;
                best_solution = q_temp;
            end
        catch
            continue;
        end
    end
    
    if isempty(best_solution)
        q_solution = [0; 0.2; 0; 0];
    else
        q_solution = best_solution;
    end
end

%% 位置误差计算函数
function error = calc_position_error_with_offset(q, target_pos, robot, offset_z)
    [~, T_current] = forward_kinematics_dh(q, robot);
    current_pos = T_current(1:3, 4);
    current_pos(3) = current_pos(3) + offset_z;
    error = norm(target_pos - current_pos');
end

%% D-H正运动学函数 
function [T_matrices, T_end] = forward_kinematics_dh(q, robot) 
    dh_params = robot.dh_params; 
    joint_types = robot.joint_types; 
    n_joints = length(q); 
 
    T_matrices = cell(n_joints, 1); 
    T_cumulative = eye(4); 
 
    for i = 1:n_joints 
        theta_0 = dh_params(i, 1); 
        d_0 = dh_params(i, 2); 
        a = dh_params(i, 3); 
        alpha = dh_params(i, 4); 
 
        if joint_types(i) == 1  % 旋转关节 
            theta = theta_0 + q(i); 
            d = d_0; 
        else  % 平移关节 
            theta = theta_0; 
            d = d_0 + q(i); 
        end 
 
        T_i = dh_transform(theta, d, a, alpha); 
        T_cumulative = T_cumulative * T_i; 
        T_matrices{i} = T_cumulative; 
    end 
 
    T_end = T_cumulative; 
end

%% 机器人绘制函数（带整体偏移）
function draw_robot_dh_with_offset(q, robot, h_blue_arm, h_yellow_slider, h_green_arm, h_purple_effector, unit_cube_vertices, offset_z)
    % 整体偏移变换
    T_offset = transl(0, 0, offset_z);

    % 蓝色臂：绕X轴旋转
    T_blue = T_offset * rotx(q(1));
    blue_vertices = transform_part(transl(robot.L_blue/2, 0, 0), ...
                                  diag([robot.L_blue, robot.W_blue, robot.W_blue]), ...
                                  T_blue, unit_cube_vertices);
    set(h_blue_arm, 'Vertices', blue_vertices);

    % 黄色滑块：在蓝色臂上滑动
    T_yellow_base = T_blue;
    T_yellow = T_yellow_base * transl(q(2), 0, robot.W_blue/2 + robot.H_yellow/2);
    yellow_vertices = transform_part(transl(0, 0, 0), ...
                                    diag([robot.L_yellow, robot.W_yellow, robot.H_yellow]), ...
                                    T_yellow, unit_cube_vertices);
    set(h_yellow_slider, 'Vertices', yellow_vertices);

    % 绿色臂：在黄色滑块上方，沿Y轴平移
    T_green_base = T_yellow * transl(0, 0, robot.H_yellow/2 + robot.W_green/2);
    T_green = T_green_base * transl(0, q(3), 0);

    green_vertices = transform_part(transl(0, robot.L_green/2, 0), ...
                                   diag([robot.W_green, robot.L_green, robot.W_green]), ...
                                   T_green, unit_cube_vertices);
    set(h_green_arm, 'Vertices', green_vertices);

    % 紫色机械臂：安装在绿色臂末端，沿X轴方向延伸
    T_green_end = T_green * transl(0, robot.L_green, 0);
    % 绕X轴旋转
    T_purple = T_green_end * rotx(q(4));

    % 紫色机械臂沿X轴方向延伸
    purple_vertices = transform_part(transl(robot.L_purple/2, 0, 0), ...
                                    diag([robot.L_purple, robot.W_purple, robot.H_purple]), ...
                                    T_purple, unit_cube_vertices);
    set(h_purple_effector, 'Vertices', purple_vertices);
end

%% 辅助函数
function q_limited = apply_joint_limits(q, robot)
    % 应用关节限制
    q_limited = q;
    for i = 1:length(q)
        q_limited(i) = max(robot.q_limits(i,1), min(robot.q_limits(i,2), q(i)));
    end
end

function T = dh_transform(theta, d, a, alpha)
    % 标准D-H变换矩阵
    ct = cos(theta); st = sin(theta);
    ca = cos(alpha); sa = sin(alpha);

    T = [ct,    -st*ca,   st*sa,    a*ct;
         st,     ct*ca,  -ct*sa,    a*st;
         0,      sa,      ca,       d;
         0,      0,       0,        1];
end

function T = rotx(theta)
    % 绕X轴旋转变换矩阵
    c = cos(theta); s = sin(theta);
    T = [1, 0,  0, 0; 0, c, -s, 0; 0, s,  c, 0; 0, 0,  0, 1];
end

function T = transl(x, y, z)
    % 平移变换矩阵
    T = eye(4);
    T(1:3, 4) = [x; y; z];
end

function V_world = transform_part(T_local_pos, S_local, T_world, V_unit)
    % 变换部件顶点
    V_model_4d = T_local_pos * [S_local*V_unit; ones(1, size(V_unit, 2))];
    V_world_4d = T_world * V_model_4d;
    V_world = V_world_4d(1:3, :)';
end
