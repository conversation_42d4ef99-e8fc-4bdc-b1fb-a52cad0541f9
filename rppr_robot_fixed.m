%% 4自由度R-P-P-R机器人 - 修正版逆运动学
clear; clc; close all; 
 
%% 机器人参数 
robot.L_blue = 1.0;         % 蓝色臂长度 
robot.W_blue = 0.06;        % 蓝色臂宽度 
robot.L_yellow = 0.15;      % 黄色滑块长度 
robot.W_yellow = 0.14;      % 黄色滑块宽度 
robot.H_yellow = 0.05;      % 黄色滑块高度 
robot.L_green = 0.6;        % 绿色臂长度 
robot.W_green = 0.04;       % 绿色臂宽度 
robot.L_purple = 0.1;       % 紫色执行器长度 
robot.W_purple = 0.07;      % 紫色执行器宽度 
robot.H_purple = 0.07;      % 紫色执行器高度 
 
% D-H参数表 
robot.dh_params = [ 
    0,       0,                                     robot.L_blue,   0;        % 关节1: 蓝色臂绕X轴旋转 
    0,       robot.W_blue/2 + robot.H_yellow/2,    0,              pi/2;     % 关节2: 黄色滑块沿X轴平移 
    0,       0,                                     0,              -pi/2;    % 关节3: 绿色臂沿Y轴平移 
    0,       robot.H_yellow/2 + robot.W_green/2,   robot.L_green + robot.L_purple,  pi/2;     % 关节4: 紫色机械臂绕X轴旋转，到达末端 
]; 
 
% 关节类型定义 (1=旋转, 0=平移) 
robot.joint_types = [1, 0, 0, 1];  % R-P-P-R 
 
% 关节限制 
green_slide_limit = (robot.W_yellow - robot.W_green) / 2; 
robot.q_limits = [ 
    -deg2rad(60),  deg2rad(60);     % q1: 蓝色臂绕X轴旋转 ±60° 
    0,             0.46;            % q2: 黄色滑块沿X轴平移 0-0.46m 
    -green_slide_limit, green_slide_limit;  % q3: 绿色臂Y轴平移 
    -pi,           pi               % q4: 紫色执行器绕X轴旋转 ±180° 
]; 

%% 机器人整体下移偏移量 
robot_offset_z = -0.6;  % 整体下移距离

fprintf('=== 4自由度R-P-P-R机器人 - 修正版逆运动学测试 ===\n'); 

%% 逆运动学验证示例 
fprintf('\n=== 逆运动学验证示例 ===\n'); 
 
% 首先用一个已知的关节角度计算正运动学，然后用逆运动学验证 
test_q = [deg2rad(30), 0.3, 0.02, deg2rad(-30)];  % 测试关节角度 
[~, T_test] = forward_kinematics_dh(test_q, robot); 
test_target = T_test(1:3, 4)';  % 提取位置 
test_target(3) = test_target(3) + robot_offset_z;  % 应用偏移 
 
fprintf('测试关节角度: q1=%.1f°, q2=%.3fm, q3=%.3fm, q4=%.1f°\n', ... 
        rad2deg(test_q(1)), test_q(2), test_q(3), rad2deg(test_q(4))); 
fprintf('正运动学计算的目标位置: [%.3f, %.3f, %.3f]\n', test_target(1), test_target(2), test_target(3)); 

% 使用修正的解析逆运动学
q_analytical_fixed = inverse_kinematics_fixed(test_target, robot, robot_offset_z); 
fprintf('修正解析逆解: q1=%.1f°, q2=%.3fm, q3=%.3fm, q4=%.1f°\n', ... 
        rad2deg(q_analytical_fixed(1)), q_analytical_fixed(2), q_analytical_fixed(3), rad2deg(q_analytical_fixed(4))); 
 
% 验证修正解析解
[~, T_verify_fixed] = forward_kinematics_dh(q_analytical_fixed, robot); 
actual_pos_fixed = T_verify_fixed(1:3, 4); 
actual_pos_fixed(3) = actual_pos_fixed(3) + robot_offset_z;  % 应用偏移
fprintf('修正解析解实际位置: [%.3f, %.3f, %.3f]\n', actual_pos_fixed(1), actual_pos_fixed(2), actual_pos_fixed(3)); 
fprintf('修正解析解位置误差: %.6f m\n', norm(test_target - actual_pos_fixed')); 

% 使用改进的数值逆运动学
q_numerical_improved = inverse_kinematics_numerical_improved(test_target, robot, robot_offset_z); 
fprintf('改进数值逆解: q1=%.1f°, q2=%.3fm, q3=%.3fm, q4=%.1f°\n', ... 
        rad2deg(q_numerical_improved(1)), q_numerical_improved(2), q_numerical_improved(3), rad2deg(q_numerical_improved(4))); 
 
% 验证改进数值解
[~, T_verify_num_improved] = forward_kinematics_dh(q_numerical_improved, robot); 
actual_pos_num_improved = T_verify_num_improved(1:3, 4); 
actual_pos_num_improved(3) = actual_pos_num_improved(3) + robot_offset_z;  % 应用偏移
fprintf('改进数值解实际位置: [%.3f, %.3f, %.3f]\n', actual_pos_num_improved(1), actual_pos_num_improved(2), actual_pos_num_improved(3)); 
fprintf('改进数值解位置误差: %.6f m\n', norm(test_target - actual_pos_num_improved'));

%% 多点测试
fprintf('\n=== 多点测试 ===\n');
test_points = [
    [1.2, 0.1, -0.5];
    [1.5, -0.2, -0.7];
    [1.8, 0.3, -0.4];
    [1.1, 0.0, -0.6];
];

for i = 1:size(test_points, 1)
    target = test_points(i, :);
    fprintf('\n测试点 %d: [%.1f, %.1f, %.1f]\n', i, target(1), target(2), target(3));
    
    % 修正解析解
    q_fixed = inverse_kinematics_fixed(target, robot, robot_offset_z);
    [~, T_check] = forward_kinematics_dh(q_fixed, robot);
    pos_check = T_check(1:3, 4)';
    pos_check(3) = pos_check(3) + robot_offset_z;
    error_fixed = norm(target - pos_check);
    
    % 改进数值解
    q_improved = inverse_kinematics_numerical_improved(target, robot, robot_offset_z);
    [~, T_check2] = forward_kinematics_dh(q_improved, robot);
    pos_check2 = T_check2(1:3, 4)';
    pos_check2(3) = pos_check2(3) + robot_offset_z;
    error_improved = norm(target - pos_check2);
    
    fprintf('  修正解析解误差: %.6f m\n', error_fixed);
    fprintf('  改进数值解误差: %.6f m\n', error_improved);
end

%% 修正的解析逆运动学函数
function q = inverse_kinematics_fixed(target_pos, robot, offset_z)
    % 修正版解析逆运动学，基于D-H参数的精确几何分析

    x_target = target_pos(1);
    y_target = target_pos(2);
    z_target = target_pos(3) - offset_z;  % 移除偏移，回到机器人坐标系

    % 步骤1: 考虑紫色机械臂保持水平的约束
    % 当紫色机械臂水平时，其末端相对于绿色臂末端的位移是 [L_purple, 0, 0]
    % 但这个位移是在绿色臂的局部坐标系中，需要考虑蓝色臂的旋转

    % 假设q1角度，计算对应的绿色臂末端位置
    % 使用迭代方法求解，因为q1和目标位置之间存在耦合

    % 初始猜测q1
    q1_guess = atan2(z_target, y_target);

    for iter = 1:10  % 迭代求解
        % 在蓝色臂旋转q1_guess后的坐标系中
        % 紫色机械臂水平意味着q4 = -q1_guess

        % 计算绿色臂末端在全局坐标系中的位置
        % 考虑紫色机械臂的贡献
        cos_q1 = cos(q1_guess);
        sin_q1 = sin(q1_guess);

        % 紫色机械臂在全局坐标系中的方向向量（水平）
        purple_dir = [1, 0, 0];  % 始终水平

        % 绿色臂末端位置 = 目标位置 - 紫色机械臂向量
        green_end_global = [x_target, y_target, z_target] - robot.L_purple * purple_dir;

        % 计算新的q1
        r_yz = sqrt(green_end_global(2)^2 + green_end_global(3)^2);
        if r_yz < 1e-6
            q1_new = 0;
        else
            q1_new = atan2(green_end_global(3), green_end_global(2));
        end

        % 检查收敛
        if abs(q1_new - q1_guess) < 1e-6
            break;
        end
        q1_guess = q1_new;
    end

    q1 = q1_guess;

    % 步骤2: 计算q2（黄色滑块的X轴平移）
    green_end_x = x_target - robot.L_purple;  % 简化：紫色机械臂水平
    q2 = green_end_x - robot.L_blue;

    % 步骤3: 计算q3（绿色臂的Y轴平移）
    % 在蓝色臂旋转后的坐标系中计算
    % 绿色臂末端在YZ平面的距离
    r_yz = sqrt(y_target^2 + z_target^2);

    % 考虑D-H参数中的各种偏移
    d2 = robot.W_blue/2 + robot.H_yellow/2;  % 黄色滑块的偏移
    d4 = robot.H_yellow/2 + robot.W_green/2;  % 绿色臂的偏移

    % 绿色臂需要达到的有效距离
    effective_distance = r_yz - d2 - d4;
    q3 = effective_distance - robot.L_green;

    % 步骤4: 计算q4（紫色机械臂保持水平）
    q4 = -q1;  % 补偿蓝色臂的旋转

    % 应用关节限制
    q = apply_joint_limits([q1, q2, q3, q4], robot);
end

%% 改进的数值逆运动学函数
function q_solution = inverse_kinematics_numerical_improved(target_pos, robot, offset_z)
    % 改进版数值逆运动学，使用更好的初始猜测和目标函数
    
    % 使用解析解作为初始猜测
    q_analytical = inverse_kinematics_fixed(target_pos, robot, offset_z);
    
    % 多个初始猜测点
    initial_guesses = [
        q_analytical(:)';  % 解析解作为第一个猜测，确保是行向量
        [0, 0.2, 0, 0];  % 中性位置
        [(robot.q_limits(1,1) + robot.q_limits(1,2))/2, ...
         (robot.q_limits(2,1) + robot.q_limits(2,2))/2, ...
         (robot.q_limits(3,1) + robot.q_limits(3,2))/2, ...
         (robot.q_limits(4,1) + robot.q_limits(4,2))/2];
    ];
    
    % 优化选项
    options = optimoptions('fmincon', 'Display', 'off', 'Algorithm', 'sqp', ...
                          'MaxIterations', 3000, 'TolFun', 1e-12, 'TolX', 1e-12, ...
                          'MaxFunctionEvaluations', 6000);
    
    % 改进的目标函数：考虑偏移的位置误差
    objective = @(q) calc_position_error_with_offset(q, target_pos, robot, offset_z);
    
    best_solution = [];
    best_error = inf;
    
    % 尝试多个初始猜测
    for i = 1:size(initial_guesses, 1)
        try
            q0 = initial_guesses(i, :)';
            [q_temp, fval] = fmincon(objective, q0, [], [], [], [], ...
                                   robot.q_limits(:,1), robot.q_limits(:,2), [], options);
            
            if fval < best_error
                best_error = fval;
                best_solution = q_temp;
            end
        catch
            continue;
        end
    end
    
    if isempty(best_solution) || best_error > 1e-3
        warning('数值逆运动学求解失败或精度不足');
        q_solution = q_analytical;  % 返回解析解作为备选
    else
        q_solution = best_solution;
    end
end

%% 带偏移的位置误差计算函数
function error = calc_position_error_with_offset(q, target_pos, robot, offset_z)
    % 计算考虑偏移的位置误差
    [~, T_current] = forward_kinematics_dh(q, robot);
    current_pos = T_current(1:3, 4);
    current_pos(3) = current_pos(3) + offset_z;  % 应用偏移
    error = norm(target_pos - current_pos');
end

%% D-H正运动学函数
function [T_matrices, T_end] = forward_kinematics_dh(q, robot)
    dh_params = robot.dh_params;
    joint_types = robot.joint_types;
    n_joints = length(q);

    T_matrices = cell(n_joints, 1);
    T_cumulative = eye(4);

    for i = 1:n_joints
        theta_0 = dh_params(i, 1);
        d_0 = dh_params(i, 2);
        a = dh_params(i, 3);
        alpha = dh_params(i, 4);

        if joint_types(i) == 1  % 旋转关节
            theta = theta_0 + q(i);
            d = d_0;
        else  % 平移关节
            theta = theta_0;
            d = d_0 + q(i);
        end

        T_i = dh_transform(theta, d, a, alpha);
        T_cumulative = T_cumulative * T_i;
        T_matrices{i} = T_cumulative;
    end

    T_end = T_cumulative;
end

%% 辅助函数
function q_limited = apply_joint_limits(q, robot)
    % 应用关节限制
    q_limited = q;
    for i = 1:length(q)
        q_limited(i) = max(robot.q_limits(i,1), min(robot.q_limits(i,2), q(i)));
    end
end

function T = dh_transform(theta, d, a, alpha)
    % 标准D-H变换矩阵
    ct = cos(theta); st = sin(theta);
    ca = cos(alpha); sa = sin(alpha);

    T = [ct,    -st*ca,   st*sa,    a*ct;
         st,     ct*ca,  -ct*sa,    a*st;
         0,      sa,      ca,       d;
         0,      0,       0,        1];
end
